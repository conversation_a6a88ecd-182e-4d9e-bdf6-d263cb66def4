import { useState, useEffect } from "react";
import Navigation from "@/components/Navigation";
import Hero from "@/components/Hero";
import PropertyGrid from "@/components/PropertyGrid";
import PropertyDetail from "@/components/PropertyDetail";
import { AdvancedSearchFilters } from "@/components/AdvancedSearchFilters";
import { PropertyComparison } from "@/components/PropertyComparison";
import { SavedSearches } from "@/components/SavedSearches";
import { AdminRedirect } from "@/components/AdminRedirect";
import { useProperties } from "@/hooks/useProperties";
import { useAuth } from "@/hooks/useAuth";
import { useProfile } from "@/hooks/useProfile";
import { useNavigate } from "react-router-dom";
import { PropertyWithImages, PropertyWithOwner, SearchFilters } from "@/types/property";

const Index = () => {
  const { user } = useAuth();
  const { profile } = useProfile();
  const navigate = useNavigate();
  const [selectedProperty, setSelectedProperty] = useState<PropertyWithOwner | null>(null);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [compareProperties, setCompareProperties] = useState<PropertyWithImages[]>([]);
  const [showSavedSearches, setShowSavedSearches] = useState(false);
  const [showAdminRedirect, setShowAdminRedirect] = useState(false);
  const { properties, loading } = useProperties();

  // Check if user is admin and show redirect modal or auto-redirect
  useEffect(() => {
    if (user && profile) {
      if (profile.user_role === 'super_admin') {
        // For super_admin, automatically redirect
        console.log('Super admin detected, redirecting to admin dashboard');
        navigate('/admin');
      } else if (profile.user_role === 'admin') {
        // For regular admin, show the redirect modal
        setShowAdminRedirect(true);
      }
    }
  }, [user, profile, navigate]);

  const handleViewDetails = (propertyId: string) => {
    const property = properties.find(p => p.id === propertyId);
    if (property) {
      // Convert PropertyWithImages to PropertyWithOwner
      // For now, we'll assume the property has profile data or use a fallback
      const propertyWithOwner: PropertyWithOwner = {
        ...property,
        profiles: property.profiles || {
          id: 'default-owner',
          full_name: 'Property Owner',
          phone: '+254700000000',
          user_role: 'landlord',
          verification_status: 'pending',
          verification_submitted_at: null,
          verification_completed_at: null,
          verified_by: null,
          national_id: null,
          business_registration_number: null,
          tax_pin: null,
          phone_verified: false,
          email_verified: false,
          phone_verification_code: null,
          phone_verification_expires_at: null,
          business_name: null,
          business_address: null,
          years_in_business: null,
          trust_score: 0.0,
          total_properties: 0,
          successful_rentals: 0,
          admin_notes: null,
          is_flagged: false,
          flagged_reason: null,
          flagged_at: null,
          flagged_by: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      };
      setSelectedProperty(propertyWithOwner);
    }
  };

  const handleBackToGrid = () => {
    setSelectedProperty(null);
  };

  const handleAddToCompare = (property: PropertyWithImages) => {
    if (compareProperties.length >= 3) {
      alert('You can compare up to 3 properties at a time');
      return;
    }
    if (!compareProperties.find(p => p.id === property.id)) {
      setCompareProperties(prev => [...prev, property]);
    }
  };

  const handleRemoveFromCompare = (propertyId: string) => {
    setCompareProperties(prev => prev.filter(p => p.id !== propertyId));
  };

  const handleClearFilters = () => {
    setSearchFilters({});
  };

  const handleLoadSavedSearch = (filters: SearchFilters) => {
    setSearchFilters(filters);
    setShowSavedSearches(false);
  };

  if (selectedProperty) {
    return (
      <div>
        <Navigation />
        <PropertyDetail 
          property={selectedProperty} 
          onBack={handleBackToGrid}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <Hero />
      
      <div className="container mx-auto px-4 py-8">
        {/* Advanced Search Filters */}
        <AdvancedSearchFilters
          filters={searchFilters}
          onFiltersChange={setSearchFilters}
          onClearFilters={handleClearFilters}
        />

        {/* Saved Searches Toggle */}
        <div className="mb-6 text-center">
          <button
            onClick={() => setShowSavedSearches(!showSavedSearches)}
            className="text-primary hover:underline text-sm"
          >
            {showSavedSearches ? 'Hide' : 'Show'} Saved Searches
          </button>
        </div>

        {/* Saved Searches */}
        {showSavedSearches && (
          <div className="mb-6">
            <SavedSearches
              currentFilters={searchFilters}
              onLoadSearch={handleLoadSavedSearch}
            />
          </div>
        )}

        {/* Property Comparison */}
        <PropertyComparison
          properties={compareProperties}
          onRemoveProperty={handleRemoveFromCompare}
          onClearAll={() => setCompareProperties([])}
        />

        {/* Property Grid */}
        <PropertyGrid 
          properties={properties}
          onViewDetails={handleViewDetails}
          onAddToCompare={handleAddToCompare}
          compareProperties={compareProperties}
          loading={loading}
          filters={searchFilters}
        />
      </div>

      {/* Admin Redirect Modal */}
      {showAdminRedirect && profile && (
        <AdminRedirect
          userRole={profile.user_role}
          userName={profile.full_name || undefined}
        />
      )}
    </div>
  );
};

export default Index;
