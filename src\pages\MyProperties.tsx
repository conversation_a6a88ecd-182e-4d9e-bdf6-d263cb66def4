import { useEffect, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ArrowLeft, Edit, Trash2, Plus, MapPin, Bed, Bath, Square, Shield, ArrowRight } from "lucide-react";
import Navigation from "@/components/Navigation";
import { useAuth } from "@/hooks/useAuth";
import { useProfile } from "@/hooks/useProfile";
import { supabase } from "@/integrations/supabase/client";
import { PropertyWithImages } from "@/types/property";
import { useToast } from "@/hooks/use-toast";

const MyProperties = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile } = useProfile();
  const { toast } = useToast();
  const [properties, setProperties] = useState<PropertyWithImages[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchMyProperties = useCallback(async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from('properties')
        .select(`
          *,
          property_images(*)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const formattedProperties = data || [];

      setProperties(formattedProperties);
    } catch (error) {
      toast({
        title: "Error fetching properties",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [user, toast]);

  const deleteProperty = async (propertyId: string) => {
    if (!confirm("Are you sure you want to delete this property?")) return;

    try {
      const { error } = await supabase
        .from('properties')
        .delete()
        .eq('id', propertyId)
        .eq('user_id', user?.id);

      if (error) throw error;

      toast({
        title: "Property deleted",
        description: "Your property has been removed successfully",
      });

      fetchMyProperties();
    } catch (error) {
      toast({
        title: "Error deleting property",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  const toggleAvailability = async (propertyId: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('properties')
        .update({ available: !currentStatus })
        .eq('id', propertyId)
        .eq('user_id', user?.id);

      if (error) throw error;

      toast({
        title: "Property updated",
        description: `Property marked as ${!currentStatus ? 'available' : 'unavailable'}`,
      });

      fetchMyProperties();
    } catch (error) {
      toast({
        title: "Error updating property",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (user) {
      fetchMyProperties();
    }
  }, [user, fetchMyProperties]);

  if (!user) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold mb-4">Authentication Required</h2>
              <p className="text-muted-foreground mb-4">Please log in to view your properties.</p>
              <Button onClick={() => navigate('/auth')}>Sign In</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">Loading your properties...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-6 sm:py-8">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" onClick={() => navigate('/')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Back</span>
              <span className="sm:hidden">Back</span>
            </Button>
            <h1 className="text-xl sm:text-2xl font-bold">My Properties</h1>
          </div>
          <Button onClick={() => navigate('/add-property')} className="w-full sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Add Property</span>
            <span className="sm:hidden">Add New</span>
          </Button>
        </div>

        {/* Verification Alert for Landlords */}
        {profile?.user_role === 'landlord' && profile?.verification_status !== 'verified' && (
          <Alert className="mb-6">
            <Shield className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <div>
                <strong>Verify your account</strong> to build trust with tenants and get more inquiries.
              </div>
              <Button 
                size="sm" 
                onClick={() => navigate('/verification')}
                className="ml-4 bg-blue-600 hover:bg-blue-700"
              >
                Get Verified
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {properties.length === 0 ? (
          <Card className="max-w-md mx-auto">
            <CardContent className="p-4 sm:p-6 text-center">
              <h2 className="text-lg sm:text-xl font-semibold mb-4">No properties yet</h2>
              <p className="text-sm sm:text-base text-muted-foreground mb-4">
                Start by adding your first property listing.
              </p>
              <Button onClick={() => navigate('/add-property')} className="w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Property
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
            {properties.map((property) => (
              <Card key={property.id} className="overflow-hidden h-full flex flex-col">
                <div className="relative">
                  <img
                    src={property.property_images?.[0]?.image_url || "/placeholder.svg"}
                    alt={property.title}
                    className="w-full h-40 sm:h-48 object-cover"
                  />
                  <div className="absolute top-2 left-2">
                    <Badge variant={property.available ? "default" : "secondary"} className="text-xs">
                      {property.available ? "Available" : "Unavailable"}
                    </Badge>
                  </div>
                  {property.featured && (
                    <div className="absolute top-2 right-2">
                      <Badge variant="outline" className="bg-background text-xs">Featured</Badge>
                    </div>
                  )}
                </div>
                
                <CardHeader className="p-3 sm:p-4 pb-2 sm:pb-3">
                  <CardTitle className="text-base sm:text-lg line-clamp-2">{property.title}</CardTitle>
                  <div className="flex items-center text-muted-foreground">
                    <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-1 flex-shrink-0" />
                    <span className="text-xs sm:text-sm truncate">{property.location}</span>
                  </div>
                </CardHeader>
                
                <CardContent className="p-3 sm:p-4 pt-0 flex-grow flex flex-col">
                  <div className="flex justify-between items-center mb-3 sm:mb-4">
                    <span className="text-lg sm:text-2xl font-bold text-primary">
                      KES {property.rent.toLocaleString()}
                    </span>
                    <span className="text-xs sm:text-sm text-muted-foreground">/month</span>
                  </div>
                  
                  <div className="flex items-center gap-2 sm:gap-4 text-xs sm:text-sm text-muted-foreground mb-3 sm:mb-4">
                    <div className="flex items-center">
                      <Bed className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                      {property.bedrooms}
                    </div>
                    <div className="flex items-center">
                      <Bath className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                      {property.bathrooms}
                    </div>
                    <div className="flex items-center">
                      <Square className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                      <span className="hidden xs:inline">{property.area} sqft</span>
                      <span className="xs:hidden">{property.area}</span>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-2 mt-auto">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => navigate(`/edit-property/${property.id}`)}
                      className="w-full sm:w-auto text-xs"
                    >
                      <Edit className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                      <span className="hidden sm:inline">Edit</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => toggleAvailability(property.id, property.available)}
                      className="w-full sm:w-auto text-xs"
                    >
                      <span className="sm:hidden">{property.available ? "Hide" : "Show"}</span>
                      <span className="hidden sm:inline">{property.available ? "Mark Unavailable" : "Mark Available"}</span>
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => deleteProperty(property.id)}
                      className="w-full sm:w-auto text-xs"
                    >
                      <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                      <span className="hidden sm:inline">Delete</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MyProperties;