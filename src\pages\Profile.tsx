import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import { useProfile } from '@/hooks/useProfile';
import { useToast } from '@/hooks/use-toast';
import { Save, ArrowLeft, User, Phone, Mail, Shield, CheckCircle, Clock, XCircle, AlertTriangle, ArrowRight } from 'lucide-react';
import Navigation from '@/components/Navigation';

const Profile: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { profile, loading, updateProfile } = useProfile();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState({
    full_name: '',
    phone: '',
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Populate form with existing profile data
  useEffect(() => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        phone: profile.phone || '',
      });
    }
  }, [profile]);

  // Redirect if not authenticated
  useEffect(() => {
    if (!user) {
      navigate('/auth');
    }
  }, [user, navigate]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Full name is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^[+]?[0-9\s\-()]{10,15}$/.test(formData.phone.trim())) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const result = await updateProfile({
        full_name: formData.full_name.trim(),
        phone: formData.phone.trim(),
      });
      
      if (result) {
        toast({
          title: "Profile updated successfully",
          description: "Your profile information has been saved.",
        });
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: "Error updating profile",
        description: "Failed to update profile. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Get verification status info
  const getVerificationStatusInfo = () => {
    if (!profile?.user_role || profile.user_role !== 'landlord') return null;
    
    switch (profile.verification_status) {
      case 'verified':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          title: 'Verified Landlord',
          description: 'Your account is fully verified and trusted by tenants.',
          badge: <Badge className="bg-green-100 text-green-800">Verified</Badge>
        };
      case 'under_review':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          title: 'Under Review',
          description: 'Your verification documents are being reviewed by our team.',
          badge: <Badge variant="secondary">Under Review</Badge>
        };
      case 'rejected':
        return {
          icon: XCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          title: 'Verification Rejected',
          description: 'Your verification was rejected. Please review and resubmit.',
          badge: <Badge variant="destructive">Rejected</Badge>
        };
      case 'suspended':
        return {
          icon: AlertTriangle,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          title: 'Account Suspended',
          description: 'Your account has been suspended. Contact support for assistance.',
          badge: <Badge variant="destructive">Suspended</Badge>
        };
      default:
        return {
          icon: Shield,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          title: 'Verification Required',
          description: 'Complete your landlord verification to build trust with tenants.',
          badge: <Badge variant="outline">Not Verified</Badge>
        };
    }
  };

  const verificationInfo = getVerificationStatusInfo();

  if (!user) {
    return null; // Will redirect
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto text-center">
          <p>Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      <div className="container mx-auto px-4 py-6 sm:py-8">
        {/* Verification Status Card for Landlords */}
        {verificationInfo && (
          <Card className="max-w-2xl mx-auto mb-6">
            <CardContent className="p-4 sm:p-6">
              <div className="flex items-start gap-4">
                <div className={`p-3 rounded-full ${verificationInfo.bgColor}`}>
                  <verificationInfo.icon className={`h-6 w-6 ${verificationInfo.color}`} />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-lg font-semibold">{verificationInfo.title}</h3>
                    {verificationInfo.badge}
                  </div>
                  <p className="text-muted-foreground mb-3">{verificationInfo.description}</p>
                  {profile?.verification_status !== 'verified' && profile?.verification_status !== 'under_review' && (
                    <Button 
                      onClick={() => navigate('/verification')} 
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Shield className="h-4 w-4 mr-2" />
                      Start Verification
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  )}
                  {profile?.verification_status === 'under_review' && (
                    <Alert>
                      <Clock className="h-4 w-4" />
                      <AlertDescription>
                        Review typically takes 2-3 business days. We'll notify you once complete.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card className="max-w-2xl mx-auto">
          <CardHeader className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
              <Button variant="outline" size="sm" onClick={() => navigate('/')} className="self-start">
                <ArrowLeft className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Back to Home</span>
                <span className="sm:hidden">Back</span>
              </Button>
            </div>
            <CardTitle className="flex items-center gap-2 text-xl sm:text-2xl">
              <User className="h-5 w-5" />
              Property Owner Profile
            </CardTitle>
            <CardDescription className="text-sm sm:text-base">
            Manage your property owner profile and contact details that will be visible to potential tenants
          </CardDescription>
        </CardHeader>
        
        <CardContent className="p-4 sm:p-6">
          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
            {/* Account Information */}
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-base sm:text-lg font-medium">Account Information</h3>
              
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm">Email Address</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    value={user.email || ''}
                    disabled
                    className="pl-10 bg-gray-50 text-sm"
                  />
                </div>
                <p className="text-xs sm:text-sm text-gray-500">
                  Email cannot be changed. Contact support if you need to update your email.
                </p>
              </div>
            </div>

            {/* Personal Information */}
            <div className="space-y-3 sm:space-y-4">
              <h3 className="text-base sm:text-lg font-medium">Personal Information</h3>
              
              <div className="space-y-2">
                <Label htmlFor="full_name" className="text-sm">Full Name *</Label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="full_name"
                    type="text"
                    value={formData.full_name}
                    onChange={(e) => handleInputChange('full_name', e.target.value)}
                    placeholder="Enter your full name"
                    className={`pl-10 text-sm ${errors.full_name ? 'border-red-500' : ''}`}
                  />
                </div>
                {errors.full_name && <p className="text-sm text-red-500">{errors.full_name}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone" className="text-sm">Phone Number *</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    id="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="e.g., +254712345678"
                    className={`pl-10 text-sm ${errors.phone ? 'border-red-500' : ''}`}
                  />
                </div>
                {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
                <p className="text-xs sm:text-sm text-gray-500">
                  Your phone number will be visible to potential tenants when you list properties.
                </p>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4 sm:pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => navigate('/')}
                disabled={isSubmitting}
                className="w-full sm:w-auto order-2 sm:order-1"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full sm:flex-1 order-1 sm:order-2"
              >
                {isSubmitting ? (
                  <>
                    <Save className="mr-2 h-4 w-4 animate-spin" />
                    Saving Profile...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Profile
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      </div>
    </div>
  );
};

export default Profile;
